<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - RestroManager</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="./manifest.json">

    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <!-- PWA Utils -->
    <script src="./pwa-utils.js"></script>

    <style>
        :root {
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --success-500: #10b981;
            --error-500: #ef4444;
            --warning-500: #f59e0b;
        }

        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }

        .main-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .left-panel {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            position: relative;
        }

        .left-panel::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 1px;
            height: 100%;
            background: linear-gradient(to bottom, transparent, #e2e8f0, transparent);
        }

        .step-section {
            display: none;
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .step-section.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .step-indicator {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .step-indicator:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }

        .step-indicator .step-circle {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .step-indicator.active .step-circle {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border-color: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
            transform: scale(1.1);
        }

        .step-indicator.completed .step-circle {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border-color: #10b981;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .step-indicator.active .step-label,
        .step-indicator.completed .step-label {
            color: #1e293b;
            font-weight: 600;
        }

        .step-indicator.active .step-sublabel,
        .step-indicator.completed .step-sublabel {
            color: #475569;
        }

        .step-indicator .step-label {
            font-weight: 500;
        }

        .step-connector {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            height: calc(100% - 2rem);
            top: 2.5rem;
        }

        .step-indicator.completed .step-connector {
            background: linear-gradient(to bottom, #10b981, #059669);
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.3);
        }

        .progress-bar {
            position: absolute;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }

        .form-input {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .error-message {
            color: #ef4444;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            animation: slideDown 0.3s ease-out;
        }

        .success-message {
            color: #10b981;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            animation: slideDown 0.3s ease-out;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            transform: translateY(-1px);
        }

        .selectable-option {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .selectable-option input:checked + label {
            border-color: #2563eb;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            color: #1d4ed8;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
            transform: translateY(-2px);
        }

        .selectable-option label:hover {
            border-color: #93c5fd;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table-row {
            animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            transition: all 0.3s ease;
        }

        .table-row:hover {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            transform: translateX(4px);
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .cuisine-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .drag-drop-area {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        }

        .drag-drop-area.dragover {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        @media (max-width: 768px) {
            .step-section {
                padding: 1rem;
            }

            .main-container {
                margin: 0.5rem;
                border-radius: 1rem;
            }

            .left-panel {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body class="bg-slate-100">

    <div class="min-h-screen flex items-center justify-center p-4">
        <!-- Progress Bar -->
        <div class="progress-bar" id="progress-bar" style="width: 16.67%"></div>

        <div class="w-full max-w-6xl flex flex-col md:flex-row main-container rounded-2xl overflow-hidden my-8">

            <!-- Left Side: Progress & Branding -->
            <div class="w-full md:w-[40%] left-panel p-8 sm:p-12">
                <a href="#" class="flex items-center gap-3 mb-12 group">
                    <div class="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg group-hover:shadow-xl transition-all duration-300">
                        <i data-lucide="chef-hat" class="w-6 h-6 text-white"></i>
                    </div>
                    <h1 class="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">RestroManager</h1>
                </a>

                <div class="mb-8">
                    <h2 class="text-lg font-semibold text-slate-700 mb-2">Setup Progress</h2>
                    <div class="w-full bg-slate-200 rounded-full h-2 mb-4">
                        <div id="progress-fill" class="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500" style="width: 16.67%"></div>
                    </div>
                    <p class="text-sm text-slate-500" id="progress-text">Step 1 of 6 - Account Details</p>
                </div>

                <div id="step-indicators-container" class="space-y-3">
                    <!-- Step indicators will be dynamically generated here -->
                </div>
            </div>

            <!-- Right Side: Form -->
            <div class="w-full md:w-[60%] p-8 sm:p-14 flex flex-col justify-center">
                <form id="signup-form">
                    <!-- Step 1: Create Account -->
                    <div id="step-1" class="step-section active">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Create your account</h2>
                            <p class="text-slate-500">Start by setting up your login details.</p>
                        </div>

                        <div class="space-y-6">
                            <button type="button" class="w-full flex items-center justify-center gap-3 py-4 border border-slate-300 rounded-xl hover:bg-slate-50 transition-all duration-300 hover:shadow-md hover:border-slate-400 group">
                                <img src="https://www.google.com/favicon.ico" alt="Google icon" class="w-5 h-5 group-hover:scale-110 transition-transform">
                                <span class="text-sm font-medium text-slate-700">Sign up with Google</span>
                            </button>

                            <div class="flex items-center">
                                <div class="flex-grow border-t border-slate-200"></div>
                                <span class="flex-shrink mx-4 text-slate-400 text-sm font-medium">OR</span>
                                <div class="flex-grow border-t border-slate-200"></div>
                            </div>

                            <div class="space-y-4">
                                <div class="form-group">
                                    <div class="relative">
                                        <i data-lucide="mail" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                                        <input type="email" name="email" required placeholder="Email address"
                                               class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div class="validation-message"></div>
                                </div>

                                <div class="form-group">
                                    <div class="relative">
                                        <i data-lucide="lock" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                                        <input type="password" name="password" required placeholder="Password (min. 8 characters)"
                                               class="form-input pl-12 pr-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <button type="button" class="toggle-password absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors">
                                            <i data-lucide="eye" class="w-5 h-5"></i>
                                        </button>
                                    </div>
                                    <div class="validation-message"></div>
                                    <div class="password-strength mt-2 hidden">
                                        <div class="flex gap-1 mb-1">
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                        </div>
                                        <p class="strength-text text-xs text-slate-500"></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <button type="button" data-next="1" class="next-btn btn-primary w-full px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Continue</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Business Details -->
                    <div id="step-2" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Tell us about your business</h2>
                            <p class="text-slate-500">This information helps us tailor your experience.</p>
                        </div>

                        <div class="space-y-6">
                            <!-- Business Logo Upload -->
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-3">Business Logo (Optional)</label>
                                <div class="drag-drop-area" id="logo-upload">
                                    <i data-lucide="upload-cloud" class="w-12 h-12 text-slate-400 mx-auto mb-3"></i>
                                    <p class="text-slate-600 font-medium">Drop your logo here or click to browse</p>
                                    <p class="text-xs text-slate-400 mt-1">PNG, JPG up to 5MB</p>
                                    <input type="file" id="logo-file" accept="image/*" class="hidden">
                                </div>
                                <div id="logo-preview" class="hidden mt-4 p-4 bg-slate-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <img id="logo-img" class="w-16 h-16 object-cover rounded-lg" alt="Logo preview">
                                        <div class="flex-1">
                                            <p class="font-medium text-slate-700" id="logo-name"></p>
                                            <p class="text-sm text-slate-500" id="logo-size"></p>
                                        </div>
                                        <button type="button" id="remove-logo" class="text-red-500 hover:text-red-700">
                                            <i data-lucide="x" class="w-5 h-5"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="business-name" class="block text-sm font-semibold text-slate-700 mb-2">Business Name</label>
                                <div class="relative">
                                    <i data-lucide="store" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="business-name" name="businessName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Enter your business name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="business-type" class="block text-sm font-semibold text-slate-700 mb-2">Business Type</label>
                                <div class="relative">
                                    <i data-lucide="utensils" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <select id="business-type" name="businessType"
                                            class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 bg-white rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select business type</option>
                                        <option value="Fine Dining">🍽️ Fine Dining</option>
                                        <option value="Casual Dining">🍕 Casual Dining</option>
                                        <option value="Cafe / Bistro">☕ Cafe / Bistro</option>
                                        <option value="Quick Service (QSR)">🍔 Quick Service (QSR)</option>
                                        <option value="Food Truck">🚚 Food Truck</option>
                                        <option value="Bakery">🥖 Bakery</option>
                                    </select>
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="annual-revenue" class="block text-sm font-semibold text-slate-700 mb-2">Estimated Annual Revenue</label>
                                <div class="relative">
                                    <i data-lucide="trending-up" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <select id="annual-revenue" name="annualRevenue"
                                            class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 bg-white rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select revenue range</option>
                                        <option value="Less than ₹50L">💰 Less than ₹50L</option>
                                        <option value="₹50L - ₹2Cr">💎 ₹50L - ₹2Cr</option>
                                        <option value="More than ₹2Cr">🏆 More than ₹2Cr</option>
                                    </select>
                                </div>
                                <div class="validation-message"></div>
                            </div>
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="0" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="button" data-next="2" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 3: Cuisine & Operations -->
                    <div id="step-3" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Cuisine & Operations</h2>
                            <p class="text-slate-500">Help customers find you by specifying your offerings.</p>
                        </div>

                        <div class="space-y-8">
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-4">Cuisine Type (select all that apply)</label>
                                <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
                                    <div class="selectable-option">
                                        <input type="checkbox" id="cuisine-indian" name="cuisine" value="Indian" class="hidden">
                                        <label for="cuisine-indian" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white">
                                            <div class="cuisine-icon">🍛</div>
                                            <div class="text-sm">Indian</div>
                                        </label>
                                    </div>
                                    <div class="selectable-option">
                                        <input type="checkbox" id="cuisine-chinese" name="cuisine" value="Chinese" class="hidden">
                                        <label for="cuisine-chinese" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white">
                                            <div class="cuisine-icon">🥢</div>
                                            <div class="text-sm">Chinese</div>
                                        </label>
                                    </div>
                                    <div class="selectable-option">
                                        <input type="checkbox" id="cuisine-italian" name="cuisine" value="Italian" class="hidden">
                                        <label for="cuisine-italian" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white">
                                            <div class="cuisine-icon">🍝</div>
                                            <div class="text-sm">Italian</div>
                                        </label>
                                    </div>
                                    <div class="selectable-option">
                                        <input type="checkbox" id="cuisine-mexican" name="cuisine" value="Mexican" class="hidden">
                                        <label for="cuisine-mexican" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white">
                                            <div class="cuisine-icon">🌮</div>
                                            <div class="text-sm">Mexican</div>
                                        </label>
                                    </div>
                                    <div class="selectable-option">
                                        <input type="checkbox" id="cuisine-japanese" name="cuisine" value="Japanese" class="hidden">
                                        <label for="cuisine-japanese" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white">
                                            <div class="cuisine-icon">🍣</div>
                                            <div class="text-sm">Japanese</div>
                                        </label>
                                    </div>
                                    <div class="selectable-option">
                                        <input type="checkbox" id="cuisine-other" name="cuisine" value="Other" class="hidden">
                                        <label for="cuisine-other" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white">
                                            <div class="cuisine-icon">🍽️</div>
                                            <div class="text-sm">Other</div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-4">Price Range</label>
                                <div class="grid grid-cols-3 gap-4">
                                    <div class="selectable-option">
                                        <input type="radio" id="price-1" name="priceRange" value="₹" class="hidden">
                                        <label for="price-1" class="block text-center p-6 border-2 border-slate-200 rounded-xl cursor-pointer transition-all hover:border-blue-300 bg-white">
                                            <div class="text-3xl font-bold text-green-600 mb-2">₹</div>
                                            <div class="text-sm font-medium text-slate-600">Budget</div>
                                            <div class="text-xs text-slate-400">₹200-500</div>
                                        </label>
                                    </div>
                                    <div class="selectable-option">
                                        <input type="radio" id="price-2" name="priceRange" value="₹₹" class="hidden" checked>
                                        <label for="price-2" class="block text-center p-6 border-2 border-slate-200 rounded-xl cursor-pointer transition-all hover:border-blue-300 bg-white">
                                            <div class="text-3xl font-bold text-blue-600 mb-2">₹₹</div>
                                            <div class="text-sm font-medium text-slate-600">Moderate</div>
                                            <div class="text-xs text-slate-400">₹500-1500</div>
                                        </label>
                                    </div>
                                    <div class="selectable-option">
                                        <input type="radio" id="price-3" name="priceRange" value="₹₹₹" class="hidden">
                                        <label for="price-3" class="block text-center p-6 border-2 border-slate-200 rounded-xl cursor-pointer transition-all hover:border-blue-300 bg-white">
                                            <div class="text-3xl font-bold text-purple-600 mb-2">₹₹₹</div>
                                            <div class="text-sm font-medium text-slate-600">Premium</div>
                                            <div class="text-xs text-slate-400">₹1500+</div>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-4">Operating Hours</label>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-xs text-slate-500 mb-1">Opening Time</label>
                                        <input type="time" name="openingTime" value="09:00"
                                               class="form-input block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-slate-500 mb-1">Closing Time</label>
                                        <input type="time" name="closingTime" value="22:00"
                                               class="form-input block w-full px-4 py-3 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="1" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="button" data-next="3" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 4: Table Configuration -->
                    <div id="step-4" class="step-section">
                        <div class="mb-8">
                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                                <div>
                                    <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">Table Configuration</h2>
                                    <p class="text-slate-500">Set up your restaurant's floor plan.</p>
                                </div>
                                <button type="button" id="add-table-btn" class="btn-primary px-6 py-3 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center gap-2 self-start">
                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                    Add Table
                                </button>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Table List -->
                            <div>
                                <h3 class="text-lg font-semibold text-slate-700 mb-4 flex items-center gap-2">
                                    <i data-lucide="list" class="w-5 h-5"></i>
                                    Table List
                                </h3>
                                <div id="table-list" class="space-y-3 max-h-80 overflow-y-auto pr-2">
                                   <!-- Dynamically added tables will go here -->
                                </div>
                                <div id="table-summary" class="mt-4 p-4 bg-slate-50 rounded-xl">
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-slate-600">Total Tables:</span>
                                        <span class="font-semibold text-slate-800" id="total-tables">0</span>
                                    </div>
                                    <div class="flex justify-between items-center text-sm mt-1">
                                        <span class="text-slate-600">Total Capacity:</span>
                                        <span class="font-semibold text-slate-800" id="total-capacity">0 seats</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Visual Floor Plan -->
                            <div>
                                <h3 class="text-lg font-semibold text-slate-700 mb-4 flex items-center gap-2">
                                    <i data-lucide="layout" class="w-5 h-5"></i>
                                    Floor Plan Preview
                                </h3>
                                <div id="floor-plan" class="bg-gradient-to-br from-slate-50 to-slate-100 border-2 border-dashed border-slate-300 rounded-xl p-6 min-h-80 relative">
                                    <div class="text-center text-slate-400 absolute inset-0 flex items-center justify-center">
                                        <div>
                                            <i data-lucide="layout" class="w-12 h-12 mx-auto mb-2 opacity-50"></i>
                                            <p class="text-sm">Tables will appear here as you add them</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="2" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="button" data-next="4" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 5: Summary -->
                    <div id="step-5" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Review & Confirm</h2>
                            <p class="text-slate-500">Please review all your information before proceeding.</p>
                        </div>

                        <div id="summary-container" class="space-y-6 text-sm bg-gradient-to-br from-slate-50 to-slate-100 p-8 rounded-xl shadow-sm">
                            <!-- Summary content will be injected here -->
                        </div>

                        <div class="mt-8">
                            <div class="flex items-center mb-6">
                                <input type="checkbox" id="terms-checkbox" class="w-5 h-5 text-blue-600 border-slate-300 rounded focus:ring-blue-500">
                                <label for="terms-checkbox" class="ml-3 text-sm text-slate-600">
                                    I agree to the <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Terms of Service</a> and <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Privacy Policy</a>
                                </label>
                            </div>

                            <div class="flex items-center gap-4">
                                <button type="button" data-prev="3" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                    Back
                                </button>
                                <button type="button" data-next="5" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <span class="btn-text">Confirm & Continue</span>
                                    <span class="btn-loading hidden">
                                        <span class="loading-spinner"></span>
                                        Processing...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 6: Personal Details -->
                    <div id="step-6" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Your Personal Details</h2>
                            <p class="text-slate-500">We need this to verify your account.</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="first-name" class="block text-sm font-semibold text-slate-700 mb-2">First Name</label>
                                <div class="relative">
                                    <i data-lucide="user" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="first-name" name="firstName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your first name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="last-name" class="block text-sm font-semibold text-slate-700 mb-2">Last Name</label>
                                <div class="relative">
                                    <i data-lucide="user" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="last-name" name="lastName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your last name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group md:col-span-2">
                                <label for="phone" class="block text-sm font-semibold text-slate-700 mb-2">Phone Number</label>
                                <div class="relative">
                                    <i data-lucide="phone" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="tel" id="phone" name="phone" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your phone number">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group md:col-span-2">
                                <label for="address" class="block text-sm font-semibold text-slate-700 mb-2">Address</label>
                                <div class="relative">
                                    <i data-lucide="map-pin" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="address" name="address" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Street address">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="city" class="block text-sm font-semibold text-slate-700 mb-2">City</label>
                                <div class="relative">
                                    <i data-lucide="building" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="city" name="city" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your city">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="pincode" class="block text-sm font-semibold text-slate-700 mb-2">Pincode</label>
                                <div class="relative">
                                    <i data-lucide="mail-open" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="pincode" name="pincode" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="6-digit pincode">
                                </div>
                                <div class="validation-message"></div>
                            </div>
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="4" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="submit" class="btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Create Account</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            lucide.createIcons();

            const steps = document.querySelectorAll('.step-section');
            const stepIndicatorsContainer = document.getElementById('step-indicators-container');
            const nextButtons = document.querySelectorAll('.next-btn');
            const prevButtons = document.querySelectorAll('.prev-btn');
            const form = document.getElementById('signup-form');
            const progressBar = document.getElementById('progress-bar');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            let currentStep = 0;
            const formData = {};

            const stepConfig = [
                { id: 'account', label: 'Account Details', sublabel: 'Enter your email and password.'},
                { id: 'business', label: 'Business Info', sublabel: 'Provide details about your restaurant.'},
                { id: 'operations', label: 'Operations', sublabel: 'Cuisines, price, and hours.'},
                { id: 'tables', label: 'Table Setup', sublabel: 'Configure your floor plan.'},
                { id: 'summary', label: 'Review', sublabel: 'Confirm your details.'},
                { id: 'personal', label: 'Personal Info', sublabel: 'Final verification step.'}
            ];

            // Validation rules
            const validationRules = {
                email: {
                    required: true,
                    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: 'Please enter a valid email address'
                },
                password: {
                    required: true,
                    minLength: 8,
                    message: 'Password must be at least 8 characters long'
                },
                businessName: {
                    required: true,
                    minLength: 2,
                    message: 'Business name must be at least 2 characters'
                },
                businessType: {
                    required: true,
                    message: 'Please select a business type'
                },
                firstName: {
                    required: true,
                    minLength: 2,
                    message: 'First name must be at least 2 characters'
                },
                lastName: {
                    required: true,
                    minLength: 2,
                    message: 'Last name must be at least 2 characters'
                },
                phone: {
                    required: true,
                    pattern: /^[0-9]{10}$/,
                    message: 'Please enter a valid 10-digit phone number'
                },
                pincode: {
                    required: true,
                    pattern: /^[0-9]{6}$/,
                    message: 'Please enter a valid 6-digit pincode'
                }
            };

            // Form validation
            const validateField = (field) => {
                const fieldName = field.name;
                const fieldValue = field.value.trim();
                const rules = validationRules[fieldName];

                if (!rules) return true;

                const fieldGroup = field.closest('.form-group');
                const validationMessage = fieldGroup.querySelector('.validation-message');

                // Reset validation state
                field.classList.remove('error', 'success');
                if (validationMessage) validationMessage.innerHTML = '';

                // Required check
                if (rules.required && fieldValue === '') {
                    field.classList.add('error');
                    if (validationMessage) {
                        validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> This field is required</div>`;
                    }
                    lucide.createIcons();
                    return false;
                }

                // Min length check
                if (rules.minLength && fieldValue.length < rules.minLength) {
                    field.classList.add('error');
                    if (validationMessage) {
                        validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> ${rules.message}</div>`;
                    }
                    lucide.createIcons();
                    return false;
                }

                // Pattern check
                if (rules.pattern && !rules.pattern.test(fieldValue)) {
                    field.classList.add('error');
                    if (validationMessage) {
                        validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> ${rules.message}</div>`;
                    }
                    lucide.createIcons();
                    return false;
                }

                // If all checks pass
                field.classList.add('success');
                if (validationMessage) {
                    validationMessage.innerHTML = `<div class="success-message"><i data-lucide="check-circle" class="w-3 h-3"></i> Looks good!</div>`;
                }
                lucide.createIcons();
                return true;
            };

            const validateStep = (stepIndex) => {
                const currentStepElement = steps[stepIndex];
                const fields = currentStepElement.querySelectorAll('input, select, textarea');
                let isValid = true;

                fields.forEach(field => {
                    if (field.type === 'checkbox' || field.type === 'radio' || field.type === 'file') return;
                    if (!validateField(field)) {
                        isValid = false;
                    }
                });

                return isValid;
            };

            // Update progress indicators and progress bar
            const updateProgress = () => {
                // Update progress bar
                const progressPercentage = ((currentStep + 1) / stepConfig.length) * 100;
                progressBar.style.width = `${progressPercentage}%`;
                progressFill.style.width = `${progressPercentage}%`;
                progressText.textContent = `Step ${currentStep + 1} of ${stepConfig.length} - ${stepConfig[currentStep].label}`;

                // Update step indicators
                stepIndicatorsContainer.innerHTML = '';
                stepConfig.forEach((step, index) => {
                    const indicator = document.createElement('a');
                    indicator.href = `#`;
                    indicator.className = 'step-indicator relative flex items-start gap-4 p-3 rounded-lg';
                    indicator.dataset.step = index;

                    let statusIcon = '';
                    if (index < currentStep) {
                        statusIcon = '<i data-lucide="check" class="w-4 h-4"></i>';
                    } else if (index === currentStep) {
                        statusIcon = `${index + 1}`;
                    } else {
                        statusIcon = `${index + 1}`;
                    }

                    indicator.innerHTML = `
                        <div class="step-circle relative z-10 w-8 h-8 rounded-full flex items-center justify-center font-bold border-2 border-slate-300 bg-white flex-shrink-0">${statusIcon}</div>
                        <div class="step-label pt-1 text-slate-500">
                            <h4 class="font-medium">${step.label}</h4>
                            <p class="text-xs">${step.sublabel}</p>
                        </div>
                        ${index < stepConfig.length - 1 ? '<div class="step-connector absolute left-4 h-full w-0.5 bg-slate-200"></div>' : ''}
                    `;

                    if (index < currentStep) {
                        indicator.classList.add('completed');
                    } else if (index === currentStep) {
                        indicator.classList.add('active');
                    }

                    // Add click event to navigate to previous steps
                    if (index <= currentStep) {
                        indicator.addEventListener('click', (e) => {
                            e.preventDefault();
                            showStep(index);
                        });
                    }

                    stepIndicatorsContainer.appendChild(indicator);
                });
                lucide.createIcons();
            };

            // Show a specific step
            const showStep = (stepIndex) => {
                // Validate current step before proceeding to next
                if (stepIndex > currentStep && !validateStep(currentStep)) {
                    return false;
                }

                // Show loading state on button
                const currentButton = document.querySelector(`button[data-next="${currentStep}"]`);
                if (currentButton) {
                    const btnText = currentButton.querySelector('.btn-text');
                    const btnLoading = currentButton.querySelector('.btn-loading');

                    if (btnText && btnLoading) {
                        btnText.classList.add('hidden');
                        btnLoading.classList.remove('hidden');

                        // Simulate processing delay
                        setTimeout(() => {
                            btnText.classList.remove('hidden');
                            btnLoading.classList.add('hidden');

                            // Actually change the step after "processing"
                            performStepChange(stepIndex);
                        }, 800);
                    } else {
                        performStepChange(stepIndex);
                    }
                } else {
                    performStepChange(stepIndex);
                }

                return true;
            };

            const performStepChange = (stepIndex) => {
                if (stepIndex === 4) { // Summary step
                    generateSummary();
                }

                steps.forEach((step, index) => {
                    step.classList.toggle('active', index === stepIndex);
                });

                currentStep = stepIndex;
                updateProgress();

                // Scroll to top of form
                form.scrollIntoView({ behavior: 'smooth', block: 'start' });
            };

            // Event listeners for navigation buttons
            nextButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const nextStepIndex = parseInt(button.dataset.next);
                    showStep(nextStepIndex);
                });
            });

            prevButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const prevStepIndex = parseInt(button.dataset.prev);
                    showStep(prevStepIndex);
                });
            });

            // Real-time validation
            form.addEventListener('input', (e) => {
                if (e.target.matches('input, select, textarea')) {
                    validateField(e.target);
                }
            });

            // Password strength indicator
            const passwordField = form.querySelector('input[name="password"]');
            const passwordStrength = passwordField.closest('.form-group').querySelector('.password-strength');

            passwordField.addEventListener('input', () => {
                const password = passwordField.value;
                const strengthBars = passwordStrength.querySelectorAll('.strength-bar');
                const strengthText = passwordStrength.querySelector('.strength-text');

                if (password.length === 0) {
                    passwordStrength.classList.add('hidden');
                    return;
                }

                passwordStrength.classList.remove('hidden');

                let strength = 0;
                let strengthLabel = '';

                if (password.length >= 8) strength++;
                if (/[a-z]/.test(password)) strength++;
                if (/[A-Z]/.test(password)) strength++;
                if (/[0-9]/.test(password)) strength++;
                if (/[^A-Za-z0-9]/.test(password)) strength++;

                strengthBars.forEach((bar, index) => {
                    bar.className = 'strength-bar h-1 rounded flex-1';
                    if (index < strength) {
                        if (strength <= 2) {
                            bar.classList.add('bg-red-400');
                            strengthLabel = 'Weak';
                        } else if (strength <= 3) {
                            bar.classList.add('bg-yellow-400');
                            strengthLabel = 'Fair';
                        } else if (strength <= 4) {
                            bar.classList.add('bg-blue-400');
                            strengthLabel = 'Good';
                        } else {
                            bar.classList.add('bg-green-400');
                            strengthLabel = 'Strong';
                        }
                    } else {
                        bar.classList.add('bg-slate-200');
                    }
                });

                strengthText.textContent = `Password strength: ${strengthLabel}`;
            });

            // Password toggle visibility
            const togglePasswordBtn = form.querySelector('.toggle-password');
            togglePasswordBtn.addEventListener('click', () => {
                const type = passwordField.type === 'password' ? 'text' : 'password';
                passwordField.type = type;

                const icon = togglePasswordBtn.querySelector('i');
                icon.setAttribute('data-lucide', type === 'password' ? 'eye' : 'eye-off');
                lucide.createIcons();
            });

            // Logo upload functionality
            const logoUpload = document.getElementById('logo-upload');
            const logoFile = document.getElementById('logo-file');
            const logoPreview = document.getElementById('logo-preview');
            const logoImg = document.getElementById('logo-img');
            const logoName = document.getElementById('logo-name');
            const logoSize = document.getElementById('logo-size');
            const removeLogo = document.getElementById('remove-logo');

            logoUpload.addEventListener('click', () => logoFile.click());

            logoUpload.addEventListener('dragover', (e) => {
                e.preventDefault();
                logoUpload.classList.add('dragover');
            });

            logoUpload.addEventListener('dragleave', () => {
                logoUpload.classList.remove('dragover');
            });

            logoUpload.addEventListener('drop', (e) => {
                e.preventDefault();
                logoUpload.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleLogoFile(files[0]);
                }
            });

            logoFile.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleLogoFile(e.target.files[0]);
                }
            });

            const handleLogoFile = (file) => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        logoImg.src = e.target.result;
                        logoName.textContent = file.name;
                        logoSize.textContent = `${(file.size / 1024).toFixed(1)} KB`;
                        logoPreview.classList.remove('hidden');
                        logoUpload.classList.add('hidden');
                    };
                    reader.readAsDataURL(file);
                }
            };

            removeLogo.addEventListener('click', () => {
                logoFile.value = '';
                logoPreview.classList.add('hidden');
                logoUpload.classList.remove('hidden');
            });

            // Enhanced Table Configuration Logic
            const tableList = document.getElementById('table-list');
            const addTableBtn = document.getElementById('add-table-btn');
            const floorPlan = document.getElementById('floor-plan');
            const totalTablesSpan = document.getElementById('total-tables');
            const totalCapacitySpan = document.getElementById('total-capacity');
            let tableCount = 0;

            const updateTableSummary = () => {
                const tables = tableList.querySelectorAll('.table-row');
                let totalCapacity = 0;

                tables.forEach(table => {
                    const capacity = parseInt(table.querySelector('input[type="number"]').value) || 0;
                    totalCapacity += capacity;
                });

                totalTablesSpan.textContent = tables.length;
                totalCapacitySpan.textContent = `${totalCapacity} seats`;

                // Update floor plan
                updateFloorPlan();
            };

            const updateFloorPlan = () => {
                const tables = tableList.querySelectorAll('.table-row');
                floorPlan.innerHTML = '';

                if (tables.length === 0) {
                    floorPlan.innerHTML = `
                        <div class="text-center text-slate-400 absolute inset-0 flex items-center justify-center">
                            <div>
                                <i data-lucide="layout" class="w-12 h-12 mx-auto mb-2 opacity-50"></i>
                                <p class="text-sm">Tables will appear here as you add them</p>
                            </div>
                        </div>
                    `;
                } else {
                    tables.forEach((table, index) => {
                        const tableName = table.querySelector('input[type="text"]').value;
                        const capacity = table.querySelector('input[type="number"]').value;

                        const tableElement = document.createElement('div');
                        tableElement.className = 'absolute bg-blue-100 border-2 border-blue-300 rounded-lg p-2 text-center text-xs font-medium cursor-move';
                        tableElement.style.left = `${20 + (index % 4) * 80}px`;
                        tableElement.style.top = `${20 + Math.floor(index / 4) * 60}px`;
                        tableElement.innerHTML = `
                            <div class="text-blue-800">${tableName}</div>
                            <div class="text-blue-600">${capacity} seats</div>
                        `;

                        floorPlan.appendChild(tableElement);
                    });
                }

                lucide.createIcons();
            };

            addTableBtn.addEventListener('click', () => {
                tableCount++;
                const tableRow = document.createElement('div');
                tableRow.className = 'table-row flex items-center gap-4 p-4 bg-white border border-slate-200 rounded-xl shadow-sm';
                tableRow.innerHTML = `
                    <div class="flex-1">
                        <label class="text-xs text-slate-500 font-medium">Table Name/No.</label>
                        <input type="text" value="Table ${tableCount}" class="w-full bg-transparent font-semibold focus:outline-none p-1 border-b border-transparent focus:border-blue-300 transition-colors">
                    </div>
                    <div class="w-24">
                        <label class="text-xs text-slate-500 font-medium">Capacity</label>
                        <input type="number" value="4" min="1" max="20" class="w-full bg-transparent font-semibold focus:outline-none p-1 border-b border-transparent focus:border-blue-300 transition-colors">
                    </div>
                    <button type="button" class="remove-table-btn text-slate-400 hover:text-red-500 p-2 rounded-lg hover:bg-red-50 transition-colors">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                `;

                tableList.appendChild(tableRow);
                lucide.createIcons();

                // Add event listeners
                tableRow.querySelector('.remove-table-btn').addEventListener('click', () => {
                    tableRow.remove();
                    updateTableSummary();
                });

                tableRow.querySelectorAll('input').forEach(input => {
                    input.addEventListener('input', updateTableSummary);
                });

                updateTableSummary();
            });

            // Enhanced Summary Generation
            const generateSummary = () => {
                const summaryContainer = document.getElementById('summary-container');
                const formElements = form.elements;

                const data = {
                    email: formElements.email.value,
                    businessName: formElements.businessName.value,
                    businessType: formElements.businessType.value,
                    annualRevenue: formElements.annualRevenue.value,
                    cuisines: Array.from(form.querySelectorAll('input[name="cuisine"]:checked')).map(el => el.value).join(', ') || 'Not specified',
                    priceRange: form.querySelector('input[name="priceRange"]:checked')?.value || 'Not specified',
                    openingTime: formElements.openingTime?.value || 'Not specified',
                    closingTime: formElements.closingTime?.value || 'Not specified',
                    tables: Array.from(tableList.querySelectorAll('.table-row')).map(row => ({
                        name: row.querySelector('input[type="text"]').value,
                        capacity: row.querySelector('input[type="number"]').value
                    }))
                };

                let tablesHtml = data.tables.map(table =>
                    `<li class="flex justify-between py-2 px-3 bg-white rounded-lg border border-slate-200">
                        <span class="font-medium">${table.name}</span>
                        <span class="text-slate-500">${table.capacity} seats</span>
                    </li>`
                ).join('');

                if (tablesHtml === '') {
                    tablesHtml = '<li class="text-slate-400 italic">No tables configured yet.</li>';
                }

                const totalCapacity = data.tables.reduce((sum, table) => sum + parseInt(table.capacity), 0);

                summaryContainer.innerHTML = `
                    <div class="space-y-6">
                        <div class="bg-white p-6 rounded-xl border border-slate-200">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-bold text-slate-800 flex items-center gap-2">
                                    <i data-lucide="user-check" class="w-5 h-5 text-blue-600"></i>
                                    Account & Business
                                </h4>
                                <button type="button" data-goto="1" class="prev-btn text-blue-600 text-sm font-semibold hover:underline px-3 py-1 rounded-lg hover:bg-blue-50 transition-colors">Edit</button>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-xs text-slate-500 uppercase tracking-wide">Email</p>
                                    <p class="font-semibold text-slate-700">${data.email}</p>
                                </div>
                                <div>
                                    <p class="text-xs text-slate-500 uppercase tracking-wide">Business Name</p>
                                    <p class="font-semibold text-slate-700">${data.businessName}</p>
                                </div>
                                <div>
                                    <p class="text-xs text-slate-500 uppercase tracking-wide">Business Type</p>
                                    <p class="font-semibold text-slate-700">${data.businessType}</p>
                                </div>
                                <div>
                                    <p class="text-xs text-slate-500 uppercase tracking-wide">Annual Revenue</p>
                                    <p class="font-semibold text-slate-700">${data.annualRevenue}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-xl border border-slate-200">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-bold text-slate-800 flex items-center gap-2">
                                    <i data-lucide="utensils" class="w-5 h-5 text-green-600"></i>
                                    Operations
                                </h4>
                                <button type="button" data-goto="2" class="prev-btn text-blue-600 text-sm font-semibold hover:underline px-3 py-1 rounded-lg hover:bg-blue-50 transition-colors">Edit</button>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-xs text-slate-500 uppercase tracking-wide">Cuisines</p>
                                    <p class="font-semibold text-slate-700">${data.cuisines}</p>
                                </div>
                                <div>
                                    <p class="text-xs text-slate-500 uppercase tracking-wide">Price Range</p>
                                    <p class="font-semibold text-slate-700">${data.priceRange}</p>
                                </div>
                                <div>
                                    <p class="text-xs text-slate-500 uppercase tracking-wide">Opening Hours</p>
                                    <p class="font-semibold text-slate-700">${data.openingTime} - ${data.closingTime}</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-xl border border-slate-200">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-bold text-slate-800 flex items-center gap-2">
                                    <i data-lucide="layout" class="w-5 h-5 text-purple-600"></i>
                                    Table Configuration
                                </h4>
                                <button type="button" data-goto="3" class="prev-btn text-blue-600 text-sm font-semibold hover:underline px-3 py-1 rounded-lg hover:bg-blue-50 transition-colors">Edit</button>
                            </div>
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                <div class="text-center p-3 bg-slate-50 rounded-lg">
                                    <p class="text-2xl font-bold text-slate-800">${data.tables.length}</p>
                                    <p class="text-xs text-slate-500">Total Tables</p>
                                </div>
                                <div class="text-center p-3 bg-slate-50 rounded-lg">
                                    <p class="text-2xl font-bold text-slate-800">${totalCapacity}</p>
                                    <p class="text-xs text-slate-500">Total Seats</p>
                                </div>
                                <div class="text-center p-3 bg-slate-50 rounded-lg">
                                    <p class="text-2xl font-bold text-slate-800">${totalCapacity > 0 ? Math.round(totalCapacity / data.tables.length) : 0}</p>
                                    <p class="text-xs text-slate-500">Avg. per Table</p>
                                </div>
                            </div>
                            <ul class="space-y-2">${tablesHtml}</ul>
                        </div>
                    </div>
                `;

                summaryContainer.querySelectorAll('.prev-btn').forEach(button => {
                    button.addEventListener('click', () => {
                        const prevStepIndex = parseInt(button.dataset.goto);
                        showStep(prevStepIndex);
                    });
                });

                lucide.createIcons();
            };

            // Enhanced Form submission with success animation
            form.addEventListener('submit', (e) => {
                e.preventDefault();

                // Validate final step
                if (!validateStep(currentStep)) {
                    return;
                }

                // Check terms checkbox
                const termsCheckbox = document.getElementById('terms-checkbox');
                if (!termsCheckbox.checked) {
                    alert('Please accept the Terms of Service and Privacy Policy to continue.');
                    return;
                }

                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const btnText = submitBtn.querySelector('.btn-text');
                const btnLoading = submitBtn.querySelector('.btn-loading');

                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
                submitBtn.disabled = true;

                // Simulate account creation
                setTimeout(() => {
                    // Store user data in localStorage for the dashboard
                    const userData = {
                        email: form.elements.email.value,
                        businessName: form.elements.businessName.value,
                        businessType: form.elements.businessType.value,
                        firstName: form.elements.firstName.value,
                        lastName: form.elements.lastName.value,
                        name: `${form.elements.firstName.value} ${form.elements.lastName.value}`,
                        role: 'admin', // Default role for new signups
                        signupDate: new Date().toISOString(),
                        isNewUser: true,
                        isLoggedIn: false // Not logged in yet, need to login after signup
                    };
                    localStorage.setItem('restroManagerUser', JSON.stringify(userData));

                    // Trigger user registered event for PWA
                    window.dispatchEvent(new CustomEvent('userRegistered', { detail: userData }));

                    // Show success message with redirect
                    const successModal = document.createElement('div');
                    successModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                    successModal.innerHTML = `
                        <div class="bg-white p-8 rounded-2xl shadow-2xl max-w-md mx-4 text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                                <i data-lucide="check" class="w-8 h-8 text-green-600"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-slate-800 mb-2">Account Created Successfully!</h3>
                            <p class="text-slate-600 mb-6">Welcome to RestroManager, ${userData.firstName}! Please login to access your dashboard.</p>
                            <div class="space-y-3">
                                <button id="redirect-login" class="btn-primary w-full px-6 py-3 text-white rounded-xl font-semibold transition-all hover:scale-105">
                                    Go to Login
                                </button>
                                <p class="text-xs text-slate-400">Redirecting automatically in <span id="countdown">5</span> seconds...</p>
                            </div>
                        </div>
                    `;

                    document.body.appendChild(successModal);
                    lucide.createIcons();

                    // Auto redirect countdown
                    let countdown = 5;
                    const countdownElement = document.getElementById('countdown');
                    const redirectBtn = document.getElementById('redirect-login');

                    const countdownInterval = setInterval(() => {
                        countdown--;
                        if (countdownElement) {
                            countdownElement.textContent = countdown;
                        }

                        if (countdown <= 0) {
                            clearInterval(countdownInterval);
                            window.location.href = 'login.html';
                        }
                    }, 1000);

                    // Manual redirect button
                    redirectBtn.addEventListener('click', () => {
                        clearInterval(countdownInterval);
                        window.location.href = 'login.html';
                    });

                    // Reset button state
                    btnText.classList.remove('hidden');
                    btnLoading.classList.add('hidden');
                    submitBtn.disabled = false;
                }, 2000);
            });

            // Initialize the form
            showStep(0);

            // Add some default tables for demo
            setTimeout(() => {
                addTableBtn.click();
                addTableBtn.click();
            }, 1000);
        });
    </script>
</body>
</html>
