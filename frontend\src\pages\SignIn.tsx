import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { RoleCard } from '../components/ui/RoleCard';
import { Logo } from '../components/ui/Logo';
import { Modal } from '../components/ui/Modal';
import { validateEmail, validatePassword } from '../utils/validation';
import { validateCredentials, createUserFromCredentials, demoAccounts } from '../utils/auth';
import { type LoginFormData } from '../types';

const roles = [
  { key: 'admin', emoji: '👨‍💼', title: 'Admin', description: 'Full Access' },
  { key: 'manager', emoji: '👩‍💼', title: 'Manager', description: 'Operations' },
  { key: 'staff', emoji: '👨‍🍳', title: 'Staff', description: 'Daily Tasks' },
  { key: 'waiter', emoji: '🧑‍🍳', title: 'Waiter', description: 'Orders' }
];

export const SignIn: React.FC = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    role: '',
    rememberMe: false
  });
  
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<LoginFormData>>({});
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState<'success' | 'error'>('success');

  const handleRoleSelect = (role: string) => {
    setFormData(prev => ({ ...prev, role }));
    setErrors(prev => ({ ...prev, role: '' }));
    
    // Auto-fill demo credentials
    const account = demoAccounts[role];
    if (account) {
      setFormData(prev => ({
        ...prev,
        email: account.email,
        password: account.password
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<LoginFormData> = {};
    
    if (!formData.role) {
      newErrors.role = 'Please select a role';
    }
    
    const emailValidation = validateEmail(formData.email);
    if (!emailValidation.isValid) {
      newErrors.email = emailValidation.message;
    }
    
    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      newErrors.password = passwordValidation.message;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      const isValid = validateCredentials(formData.email, formData.password, formData.role);
      
      if (isValid) {
        const user = createUserFromCredentials(formData.email, formData.role);
        login(user);
        setModalType('success');
        setShowModal(true);
        
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      } else {
        setModalType('error');
        setShowModal(true);
      }
      
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md glass-card rounded-2xl overflow-hidden my-8">
        {/* Header */}
        <div className="p-8 pb-6 text-center">
          <div className="mb-8">
            <Logo size="md" />
          </div>
          
          <div className="mb-6">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">
              Welcome Back
            </h2>
            <p className="text-slate-500">Sign in to your restaurant dashboard</p>
          </div>
        </div>
        
        {/* Login Form */}
        <div className="px-8 pb-8">
          <form onSubmit={handleSubmit}>
            {/* Role Selection */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-slate-700 mb-3">Login as</label>
              <div className="grid grid-cols-2 gap-3">
                {roles.map((role) => (
                  <RoleCard
                    key={role.key}
                    role={role.key}
                    emoji={role.emoji}
                    title={role.title}
                    description={role.description}
                    isSelected={formData.role === role.key}
                    onClick={() => handleRoleSelect(role.key)}
                  />
                ))}
              </div>
              {errors.role && (
                <div className="error-message mt-2">
                  <span className="text-xs">⚠</span>
                  {errors.role}
                </div>
              )}
            </div>
            
            {/* Email Input */}
            <Input
              type="email"
              label="Email Address"
              placeholder="Enter your email"
              icon={Mail}
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              error={errors.email}
              required
            />
            
            {/* Password Input */}
            <Input
              type={showPassword ? 'text' : 'password'}
              label="Password"
              placeholder="Enter your password"
              icon={Lock}
              value={formData.password}
              onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
              error={errors.password}
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-slate-400 hover:text-slate-600 transition-colors"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              }
              required
            />
            
            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between mb-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={(e) => setFormData(prev => ({ ...prev, rememberMe: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-slate-600">Remember me</span>
              </label>
              <a href="#" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                Forgot password?
              </a>
            </div>
            
            {/* Login Button */}
            <Button
              type="submit"
              variant="primary"
              size="lg"
              loading={isLoading}
              className="w-full"
            >
              Sign In
            </Button>
            
            {/* Divider */}
            <div className="flex items-center my-6">
              <div className="flex-grow border-t border-slate-200"></div>
              <span className="flex-shrink mx-4 text-slate-400 text-sm font-medium">OR</span>
              <div className="flex-grow border-t border-slate-200"></div>
            </div>
            
            {/* Sign Up Link */}
            <div className="text-center">
              <p className="text-sm text-slate-600">
                Don't have an account?{' '}
                <Link to="/signup" className="text-blue-600 hover:text-blue-800 font-semibold">
                  Create one here
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>

      {/* Success/Error Modal */}
      <Modal isOpen={showModal} onClose={() => setShowModal(false)}>
        <div className="text-center">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
            modalType === 'success' ? 'bg-green-100' : 'bg-red-100'
          }`}>
            <span className="text-2xl">
              {modalType === 'success' ? '✓' : '✗'}
            </span>
          </div>
          <h3 className="text-2xl font-bold text-slate-800 mb-2">
            {modalType === 'success' ? 'Welcome Back!' : 'Login Failed'}
          </h3>
          <p className="text-slate-600 mb-6">
            {modalType === 'success' 
              ? `Successfully logged in as ${formData.role}. Redirecting to dashboard...`
              : 'Invalid email or password. Please try again.'
            }
          </p>
          {modalType === 'error' && (
            <Button onClick={() => setShowModal(false)} variant="primary">
              Try Again
            </Button>
          )}
        </div>
      </Modal>
    </div>
  );
};
