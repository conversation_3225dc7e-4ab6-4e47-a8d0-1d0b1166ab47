import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  User, Mail, Lock, Phone, MapPin, Building, Store, Utensils,
  ChefHat, ArrowLeft, ArrowRight, Check, AlertCircle,
  Eye, EyeOff, Plus, LayoutGrid, Move3D, Trash2
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';

// Step configuration matching the original signup.html
const stepConfig = [
  { id: 'account', label: 'Account' },
  { id: 'business', label: 'Business & Cuisine' },
  { id: 'operations', label: 'Operations' },
  { id: 'tables', label: 'Tables' },
  { id: 'summary', label: 'Review' },
  { id: 'personal', label: 'Personal' }
];

// Business types from original
const businessTypes = [
  { value: 'Fine Dining', label: '🍽️ Fine Dining' },
  { value: 'Casual Dining', label: '🍕 Casual Dining' },
  { value: 'Cafe / Bistro', label: '☕ Cafe / Bistro' },
  { value: 'Quick Service (QSR)', label: '🍔 Quick Service (QSR)' },
  { value: 'Food Truck', label: '🚚 Food Truck' },
  { value: 'Pub / Bar', label: '🍺 Pub / Bar' },
  { value: 'Bakery', label: '🥖 Bakery' }
];

// Cuisine types from original
const cuisineTypes = [
  { value: 'British', label: 'British', emoji: '🇬🇧' },
  { value: 'Indian', label: 'Indian', emoji: '🍛' },
  { value: 'Italian', label: 'Italian', emoji: '🍝' },
  { value: 'Chinese', label: 'Chinese', emoji: '🥢' },
  { value: 'American', label: 'American', emoji: '🍔' },
  { value: 'Other', label: 'Other', emoji: '🍽️' }
];



// Days of the week for operations
const daysOfWeek = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
];

// Validation rule types
interface ValidationRule {
  required: boolean;
  minLength?: number;
  pattern?: RegExp;
  message: string;
}

// Validation rules (UK-specific from original)
const validationRules: Record<string, ValidationRule> = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: 'Please enter a valid email address'
  },
  password: {
    required: true,
    minLength: 8,
    message: 'Password must be at least 8 characters long'
  },
  businessName: {
    required: true,
    minLength: 2,
    message: 'Business name must be at least 2 characters'
  },
  businessType: {
    required: true,
    message: 'Please select a business type'
  },
  firstName: {
    required: true,
    minLength: 2,
    message: 'First name must be at least 2 characters'
  },
  lastName: {
    required: true,
    minLength: 2,
    message: 'Last name must be at least 2 characters'
  },
  phone: {
    required: true,
    pattern: /^(?:(?:\(?(?:0(?:0|11)\)?[\s-]?\(?|0)44\)?[\s-]?(?:\(?0\)?[\s-]?)?)|(?:\(?0))(?:(?:\d{5}\)?[\s-]?\d{4,5})|(?:\d{4}\)?[\s-]?(?:\d{5}|\d{3}[\s-]?\d{3}))|(?:\d{3}\)?[\s-]?\d{3}[\s-]?\d{3,4})|(?:\d{2}\)?[\s-]?\d{4}[\s-]?\d{4}))(?:[\s-]?\d{4})?$/,
    message: 'Please enter a valid UK phone number'
  },
  address: {
    required: true,
    minLength: 5,
    message: 'Please enter a valid address'
  },
  city: {
    required: true,
    minLength: 2,
    message: 'Please enter a valid city'
  },
  postcode: {
    required: true,
    pattern: /^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9][A-Za-z]?))))\s?[0-9][A-Za-z]{2})$/,
    message: 'Please enter a valid UK postcode'
  }
};

export const SignUp: React.FC = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalContent, setModalContent] = useState({ title: '', message: '' });
  const [showAddSectionModal, setShowAddSectionModal] = useState(false);
  const [newSectionName, setNewSectionName] = useState('');

  // Form data state matching original signup.html structure
  const [formData, setFormData] = useState({
    // Step 1: Account Details
    email: '',
    password: '',

    // Step 2: Business & Cuisine
    businessName: '',
    businessType: '',
    cuisine: [] as string[],

    // Step 3: Operations & Daily Revenue
    operations: {} as Record<string, {
      isOpen: boolean;
      shifts: Array<{
        name: string;
        startTime: string;
        endTime: string;
        staff: number;
        expectedRevenue: number;
      }>;
    }>,

    // Step 4: Table Configuration
    layoutStyle: 'section' as 'section' | 'flow',
    tableSections: [] as Array<{
      id: string;
      name: string;
      tables: Array<{
        id: string;
        name: string;
        seats: number;
        type: string;
      }>;
    }>,
    flowTables: [] as Array<{
      id: string;
      name: string;
      seats: number;
      type: string;
    }>,

    // Step 6: Personal Details
    firstName: '',
    lastName: '',
    phone: '',
    address: '',
    city: '',
    postcode: '',

    // Terms acceptance
    termsAccepted: false
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    text: '',
    color: ''
  });

  // Initialize operations for all days
  useEffect(() => {
    const initialOperations: typeof formData.operations = {};
    daysOfWeek.forEach(day => {
      initialOperations[day] = {
        isOpen: true,
        shifts: [
          {
            name: 'Lunch',
            startTime: '12:00',
            endTime: '15:00',
            staff: 3,
            expectedRevenue: 500
          },
          {
            name: 'Dinner',
            startTime: '18:00',
            endTime: '22:00',
            staff: 5,
            expectedRevenue: 800
          }
        ]
      };
    });
    setFormData(prev => ({ ...prev, operations: initialOperations }));
  }, []);

  // Password strength calculation
  const calculatePasswordStrength = (password: string) => {
    let score = 0;
    let text = '';
    let color = '';

    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    switch (score) {
      case 0:
      case 1:
        text = 'Very Weak';
        color = 'bg-red-500';
        break;
      case 2:
        text = 'Weak';
        color = 'bg-red-400';
        break;
      case 3:
        text = 'Fair';
        color = 'bg-yellow-500';
        break;
      case 4:
        text = 'Good';
        color = 'bg-blue-500';
        break;
      case 5:
        text = 'Strong';
        color = 'bg-green-500';
        break;
    }

    return { score, text, color };
  };

  // Field validation function
  const validateField = (fieldName: string, value: string): boolean => {
    const rules = validationRules[fieldName as keyof typeof validationRules];
    if (!rules) return true;

    const newErrors = { ...errors };
    delete newErrors[fieldName];

    if (rules.required && value.trim() === '') {
      newErrors[fieldName] = 'This field is required';
      setErrors(newErrors);
      return false;
    }

    if (rules.minLength && value.length < rules.minLength) {
      newErrors[fieldName] = rules.message;
      setErrors(newErrors);
      return false;
    }

    if (rules.pattern && !rules.pattern.test(value)) {
      newErrors[fieldName] = rules.message;
      setErrors(newErrors);
      return false;
    }

    setErrors(newErrors);
    return true;
  };

  // Step validation
  const validateStep = (stepIndex: number): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    switch (stepIndex) {
      case 0: // Account Details
        if (!validateField('email', formData.email)) isValid = false;
        if (!validateField('password', formData.password)) isValid = false;
        break;
      case 1: // Business & Cuisine
        if (!validateField('businessName', formData.businessName)) isValid = false;
        if (!formData.businessType) {
          newErrors.businessType = 'Please select a business type';
          isValid = false;
        }
        if (formData.cuisine.length === 0) {
          newErrors.cuisine = 'Please select at least one cuisine type';
          isValid = false;
        }
        break;
      case 2: // Operations - basic validation
        const hasOpenDay = Object.values(formData.operations).some(day => day.isOpen);
        if (!hasOpenDay) {
          newErrors.operations = 'Please have at least one day open';
          isValid = false;
        }
        break;
      case 3: // Tables
        const tableCount = formData.layoutStyle === 'section'
          ? formData.tableSections.reduce((total, section) => total + section.tables.length, 0)
          : formData.flowTables.length;
        if (tableCount === 0) {
          newErrors.tables = 'Please add at least one table';
          isValid = false;
        }
        break;
      case 4: // Summary - check terms
        if (!formData.termsAccepted) {
          newErrors.termsAccepted = 'Please accept the terms and conditions';
          isValid = false;
        }
        break;
      case 5: // Personal Details
        if (!validateField('firstName', formData.firstName)) isValid = false;
        if (!validateField('lastName', formData.lastName)) isValid = false;
        if (!validateField('phone', formData.phone)) isValid = false;
        if (!validateField('address', formData.address)) isValid = false;
        if (!validateField('city', formData.city)) isValid = false;
        if (!validateField('postcode', formData.postcode)) isValid = false;
        break;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Navigation handlers
  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
  };

  // Form input handlers
  const handleInputChange = (field: string, value: string | string[] | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      const newErrors = { ...errors };
      delete newErrors[field];
      setErrors(newErrors);
    }

    // Update password strength
    if (field === 'password' && typeof value === 'string') {
      setPasswordStrength(calculatePasswordStrength(value));
    }
  };

  // Cuisine selection handler
  const handleCuisineToggle = (cuisineValue: string) => {
    const newCuisine = formData.cuisine.includes(cuisineValue)
      ? formData.cuisine.filter(c => c !== cuisineValue)
      : [...formData.cuisine, cuisineValue];
    handleInputChange('cuisine', newCuisine);
  };

  // Operations handlers
  const updateOperations = (day: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      operations: {
        ...prev.operations,
        [day]: {
          ...prev.operations[day],
          [field]: value
        }
      }
    }));
  };

  const updateShift = (day: string, shiftIndex: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      operations: {
        ...prev.operations,
        [day]: {
          ...prev.operations[day],
          shifts: prev.operations[day].shifts.map((shift, index) =>
            index === shiftIndex ? { ...shift, [field]: value } : shift
          )
        }
      }
    }));
  };

  const addShift = (day: string) => {
    const newShift = {
      name: 'New Shift',
      startTime: '09:00',
      endTime: '17:00',
      staff: 2,
      expectedRevenue: 300
    };

    setFormData(prev => ({
      ...prev,
      operations: {
        ...prev.operations,
        [day]: {
          ...prev.operations[day],
          shifts: [...prev.operations[day].shifts, newShift]
        }
      }
    }));
  };

  const removeShift = (day: string, shiftIndex: number) => {
    setFormData(prev => ({
      ...prev,
      operations: {
        ...prev.operations,
        [day]: {
          ...prev.operations[day],
          shifts: prev.operations[day].shifts.filter((_, index) => index !== shiftIndex)
        }
      }
    }));
  };

  // Table management handlers
  const addTableSection = (sectionName: string) => {
    const newSection = {
      id: Date.now().toString(),
      name: sectionName,
      tables: []
    };
    setFormData(prev => ({
      ...prev,
      tableSections: [...prev.tableSections, newSection]
    }));
  };

  const addTableToSection = (sectionId: string) => {
    const newTable = {
      id: Date.now().toString(),
      name: `Table ${Date.now()}`,
      seats: 4,
      type: 'Standard'
    };
    setFormData(prev => ({
      ...prev,
      tableSections: prev.tableSections.map(section =>
        section.id === sectionId
          ? { ...section, tables: [...section.tables, newTable] }
          : section
      )
    }));
  };

  const removeTableFromSection = (sectionId: string, tableId: string) => {
    setFormData(prev => ({
      ...prev,
      tableSections: prev.tableSections.map(section =>
        section.id === sectionId
          ? { ...section, tables: section.tables.filter(table => table.id !== tableId) }
          : section
      )
    }));
  };

  const addFlowTable = () => {
    const newTable = {
      id: Date.now().toString(),
      name: `Table ${Date.now()}`,
      seats: 4,
      type: 'Standard'
    };
    setFormData(prev => ({
      ...prev,
      flowTables: [...prev.flowTables, newTable]
    }));
  };

  const removeFlowTable = (tableId: string) => {
    setFormData(prev => ({
      ...prev,
      flowTables: prev.flowTables.filter(table => table.id !== tableId)
    }));
  };

  // Final submission
  const handleSubmit = async () => {
    if (!validateStep(5)) return;

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const userData = {
        email: formData.email,
        role: 'admin' as any,
        name: `${formData.firstName} ${formData.lastName}`,
        loginDate: new Date().toISOString(),
        isLoggedIn: true
      };

      login(userData);

      setModalContent({
        title: 'Account Created Successfully!',
        message: 'Welcome to RestroManager. You will be redirected to your dashboard shortly.'
      });
      setShowModal(true);

      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);

    } catch (error) {
      setModalContent({
        title: 'Error',
        message: 'There was an error creating your account. Please try again.'
      });
      setShowModal(true);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate summary for step 5
  const generateSummary = () => {
    return {
      account: { email: formData.email },
      business: {
        name: formData.businessName,
        type: formData.businessType,
        cuisine: formData.cuisine.join(', ')
      },
      operations: Object.keys(formData.operations).filter(day => formData.operations[day].isOpen),
      tables: formData.layoutStyle === 'section'
        ? formData.tableSections.reduce((total, section) => total + section.tables.length, 0)
        : formData.flowTables.length,
      personal: {
        name: `${formData.firstName} ${formData.lastName}`,
        phone: formData.phone,
        address: `${formData.address}, ${formData.city}, ${formData.postcode}`
      }
    };
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Account Details
        return (
          <div className="space-y-6">
            <div className="mb-8">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">
                Create your account
              </h2>
              <p className="text-slate-500">Start by setting up your login details.</p>
            </div>

            <div className="space-y-6">
              <button
                type="button"
                className="w-full flex items-center justify-center gap-3 py-4 border border-slate-300 rounded-xl hover:bg-slate-50 transition-all duration-300 hover:shadow-md hover:border-slate-400 group"
              >
                <img src="https://www.google.com/favicon.ico" alt="Google icon" className="w-5 h-5 group-hover:scale-110 transition-transform" />
                <span className="text-sm font-medium text-slate-700">Sign up with Google</span>
              </button>

              <div className="flex items-center">
                <div className="flex-grow border-t border-slate-200"></div>
                <span className="flex-shrink mx-4 text-slate-400 text-sm font-medium">OR</span>
                <div className="flex-grow border-t border-slate-200"></div>
              </div>

              <div className="space-y-4">
                <div className="form-group">
                  <div className="relative">
                    <Mail className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                    <input
                      type="email"
                      name="email"
                      required
                      placeholder="Email address"
                      className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.email ? 'error' : ''}`}
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                    />
                  </div>
                  {errors.email && (
                    <div className="error-message">
                      <AlertCircle className="w-3 h-3" />
                      {errors.email}
                    </div>
                  )}
                </div>

                <div className="form-group">
                  <div className="relative">
                    <Lock className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      required
                      placeholder="Password (min. 8 characters)"
                      className={`form-input pl-12 pr-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.password ? 'error' : ''}`}
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                    />
                    <button
                      type="button"
                      className="absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                  {errors.password && (
                    <div className="error-message">
                      <AlertCircle className="w-3 h-3" />
                      {errors.password}
                    </div>
                  )}
                  {formData.password && (
                    <div className="password-strength mt-2">
                      <div className="flex gap-1 mb-1">
                        {[1, 2, 3, 4].map((level) => (
                          <div
                            key={level}
                            className={`h-1 bg-slate-200 rounded flex-1 ${
                              passwordStrength.score >= level ? passwordStrength.color : ''
                            }`}
                          />
                        ))}
                      </div>
                      <p className="text-xs text-slate-500">{passwordStrength.text}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 1: // Business & Cuisine
        return (
          <div className="space-y-6">
            <div className="mb-8">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">
                Business & Cuisine
              </h2>
              <p className="text-slate-500">Tell us about your business and what you serve.</p>
            </div>

            <div className="space-y-6">
              <div className="form-group">
                <label htmlFor="business-name" className="block text-sm font-semibold text-slate-700 mb-2">
                  Business Name
                </label>
                <div className="relative">
                  <Store className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <input
                    type="text"
                    id="business-name"
                    name="businessName"
                    required
                    className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.businessName ? 'error' : ''}`}
                    placeholder="Enter your business name"
                    value={formData.businessName}
                    onChange={(e) => handleInputChange('businessName', e.target.value)}
                  />
                </div>
                {errors.businessName && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.businessName}
                  </div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="business-type" className="block text-sm font-semibold text-slate-700 mb-2">
                  Business Type
                </label>
                <div className="relative">
                  <Utensils className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <select
                    id="business-type"
                    name="businessType"
                    className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 bg-white rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.businessType ? 'error' : ''}`}
                    value={formData.businessType}
                    onChange={(e) => handleInputChange('businessType', e.target.value)}
                  >
                    <option value="">Select business type</option>
                    {businessTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>
                {errors.businessType && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.businessType}
                  </div>
                )}
              </div>

              <div className="form-group">
                <label className="block text-sm font-semibold text-slate-700 mb-4">
                  Cuisine Type (select all that apply)
                </label>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  {cuisineTypes.map((cuisine) => (
                    <div key={cuisine.value} className="selectable-option">
                      <input
                        type="checkbox"
                        id={`cuisine-${cuisine.value}`}
                        name="cuisine"
                        value={cuisine.value}
                        className="hidden"
                        checked={formData.cuisine.includes(cuisine.value)}
                        onChange={() => handleCuisineToggle(cuisine.value)}
                      />
                      <label
                        htmlFor={`cuisine-${cuisine.value}`}
                        className={`block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white ${
                          formData.cuisine.includes(cuisine.value)
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : ''
                        }`}
                      >
                        <div className="text-2xl mb-2">{cuisine.emoji}</div>
                        <div className="text-sm">{cuisine.label}</div>
                      </label>
                    </div>
                  ))}
                </div>
                {errors.cuisine && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.cuisine}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 2: // Operations & Daily Revenue
        return (
          <div className="space-y-6">
            <div className="mb-8">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">
                Operations & Daily Revenue
              </h2>
              <p className="text-slate-500">Plan your staffing and operations for each service with precision.</p>
            </div>

            <div className="space-y-6 max-h-[60vh] overflow-y-auto pr-4">
              {daysOfWeek.map((day) => (
                <div key={day} className="bg-white border border-slate-200 rounded-xl p-6 shadow-sm">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-slate-800">{day}</h3>
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={formData.operations[day]?.isOpen || false}
                        onChange={(e) => updateOperations(day, 'isOpen', e.target.checked)}
                        className="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                      />
                      <span className="text-sm text-slate-600">Open</span>
                    </label>
                  </div>

                  {formData.operations[day]?.isOpen && (
                    <div className="space-y-3">
                      {formData.operations[day]?.shifts.map((shift, shiftIndex) => (
                        <div key={shiftIndex} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-4 bg-slate-50 rounded-lg border">
                          <input
                            type="text"
                            placeholder="Shift name"
                            value={shift.name}
                            onChange={(e) => updateShift(day, shiftIndex, 'name', e.target.value)}
                            className="px-3 py-2 border border-slate-300 rounded-md text-sm"
                          />
                          <input
                            type="time"
                            value={shift.startTime}
                            onChange={(e) => updateShift(day, shiftIndex, 'startTime', e.target.value)}
                            className="px-3 py-2 border border-slate-300 rounded-md text-sm"
                          />
                          <input
                            type="time"
                            value={shift.endTime}
                            onChange={(e) => updateShift(day, shiftIndex, 'endTime', e.target.value)}
                            className="px-3 py-2 border border-slate-300 rounded-md text-sm"
                          />
                          <input
                            type="number"
                            placeholder="Staff"
                            min="1"
                            value={shift.staff}
                            onChange={(e) => updateShift(day, shiftIndex, 'staff', parseInt(e.target.value) || 1)}
                            className="px-3 py-2 border border-slate-300 rounded-md text-sm"
                          />
                          <div className="flex items-center gap-2">
                            <input
                              type="number"
                              placeholder="Revenue"
                              min="0"
                              value={shift.expectedRevenue}
                              onChange={(e) => updateShift(day, shiftIndex, 'expectedRevenue', parseInt(e.target.value) || 0)}
                              className="px-3 py-2 border border-slate-300 rounded-md text-sm flex-1"
                            />
                            <button
                              type="button"
                              onClick={() => removeShift(day, shiftIndex)}
                              className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => addShift(day)}
                        className="w-full py-2 border-2 border-dashed border-slate-300 rounded-lg text-slate-500 hover:border-blue-400 hover:text-blue-600 transition-colors flex items-center justify-center gap-2"
                      >
                        <Plus className="w-4 h-4" />
                        Add Shift
                      </button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );

      case 3: // Table Configuration
        return (
          <div className="space-y-6">
            <div className="mb-6">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">
                Table Configuration
              </h2>
              <p className="text-slate-500">Design your restaurant's floor plan.</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left: Controls */}
              <div className="space-y-6">
                <div className="form-group">
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Layout Style</label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="selectable-option">
                      <input
                        type="radio"
                        id="layout-section"
                        name="layoutStyle"
                        value="section"
                        className="hidden"
                        checked={formData.layoutStyle === 'section'}
                        onChange={(e) => handleInputChange('layoutStyle', e.target.value)}
                      />
                      <label
                        htmlFor="layout-section"
                        className={`block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium ${
                          formData.layoutStyle === 'section' ? 'border-blue-500 bg-blue-50 text-blue-700' : ''
                        }`}
                      >
                        <LayoutGrid className="mx-auto mb-2 w-6 h-6" />
                        Section-based
                      </label>
                    </div>
                    <div className="selectable-option">
                      <input
                        type="radio"
                        id="layout-flow"
                        name="layoutStyle"
                        value="flow"
                        className="hidden"
                        checked={formData.layoutStyle === 'flow'}
                        onChange={(e) => handleInputChange('layoutStyle', e.target.value)}
                      />
                      <label
                        htmlFor="layout-flow"
                        className={`block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium ${
                          formData.layoutStyle === 'flow' ? 'border-blue-500 bg-blue-50 text-blue-700' : ''
                        }`}
                      >
                        <Move3D className="mx-auto mb-2 w-6 h-6" />
                        Free Flow
                      </label>
                    </div>
                  </div>
                </div>

                {/* Section Based UI */}
                {formData.layoutStyle === 'section' && (
                  <div>
                    <button
                      type="button"
                      onClick={() => setShowAddSectionModal(true)}
                      className="w-full btn-secondary py-3 text-sm font-semibold rounded-lg flex items-center justify-center gap-2 border border-slate-300 hover:border-slate-400 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      Add New Section
                    </button>
                    <div className="mt-4 space-y-4 max-h-[400px] overflow-y-auto pr-2">
                      {formData.tableSections.map((section) => (
                        <div key={section.id} className="border border-slate-200 rounded-lg p-4 bg-white">
                          <h4 className="font-semibold text-slate-700 mb-3">{section.name}</h4>
                          <div className="space-y-2">
                            {section.tables.map((table) => (
                              <div key={table.id} className="flex items-center justify-between p-2 bg-slate-50 rounded">
                                <span className="text-sm">{table.name} ({table.seats} seats)</span>
                                <button
                                  type="button"
                                  onClick={() => removeTableFromSection(section.id, table.id)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            ))}
                            <button
                              type="button"
                              onClick={() => addTableToSection(section.id)}
                              className="w-full py-2 border border-dashed border-slate-300 rounded text-slate-500 hover:border-blue-400 hover:text-blue-600 transition-colors text-sm"
                            >
                              + Add Table
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Flow Based UI */}
                {formData.layoutStyle === 'flow' && (
                  <div>
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-slate-700">Tables</h3>
                      <button
                        type="button"
                        onClick={addFlowTable}
                        className="btn-secondary px-4 py-2 text-sm font-semibold text-slate-700 rounded-lg flex items-center gap-2 border border-slate-300 hover:border-slate-400 transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                        Add Table
                      </button>
                    </div>
                    <div className="mt-4 space-y-3 max-h-[400px] overflow-y-auto pr-2">
                      {formData.flowTables.map((table) => (
                        <div key={table.id} className="flex items-center justify-between p-3 bg-white border border-slate-200 rounded-lg">
                          <span className="text-sm">{table.name} ({table.seats} seats, {table.type})</span>
                          <button
                            type="button"
                            onClick={() => removeFlowTable(table.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Right: Preview */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-slate-700">Floor Plan Preview</h3>
                <div className="bg-gradient-to-br from-slate-50 to-slate-100 border-2 border-dashed border-slate-300 rounded-xl p-4 min-h-[400px] flex items-center justify-center">
                  <div className="text-center text-slate-500">
                    <Building className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Floor plan preview will appear here</p>
                    <p className="text-xs mt-1">
                      {formData.layoutStyle === 'section'
                        ? `${formData.tableSections.reduce((total, section) => total + section.tables.length, 0)} tables in ${formData.tableSections.length} sections`
                        : `${formData.flowTables.length} tables`
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
            {errors.tables && (
              <div className="error-message">
                <AlertCircle className="w-3 h-3" />
                {errors.tables}
              </div>
            )}
          </div>
        );

      case 4: // Summary
        const summary = generateSummary();
        return (
          <div className="space-y-6">
            <div className="mb-8">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">
                Review & Confirm
              </h2>
              <p className="text-slate-500">Please review all your information before proceeding.</p>
            </div>

            <div className="space-y-6 text-sm bg-gradient-to-br from-slate-50 to-slate-100 p-8 rounded-xl shadow-sm">
              <div>
                <h3 className="font-semibold text-slate-800 mb-2">Account Details</h3>
                <p className="text-slate-600">Email: {summary.account.email}</p>
              </div>

              <div>
                <h3 className="font-semibold text-slate-800 mb-2">Business Information</h3>
                <p className="text-slate-600">Name: {summary.business.name}</p>
                <p className="text-slate-600">Type: {summary.business.type}</p>
                <p className="text-slate-600">Cuisine: {summary.business.cuisine}</p>
              </div>

              <div>
                <h3 className="font-semibold text-slate-800 mb-2">Operations</h3>
                <p className="text-slate-600">Open Days: {summary.operations.join(', ')}</p>
              </div>

              <div>
                <h3 className="font-semibold text-slate-800 mb-2">Tables</h3>
                <p className="text-slate-600">Total Tables: {summary.tables}</p>
                <p className="text-slate-600">Layout: {formData.layoutStyle === 'section' ? 'Section-based' : 'Free Flow'}</p>
              </div>

              {summary.personal.name && (
                <div>
                  <h3 className="font-semibold text-slate-800 mb-2">Personal Details</h3>
                  <p className="text-slate-600">Name: {summary.personal.name}</p>
                  <p className="text-slate-600">Phone: {summary.personal.phone}</p>
                  <p className="text-slate-600">Address: {summary.personal.address}</p>
                </div>
              )}
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="terms-checkbox"
                className="w-5 h-5 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                checked={formData.termsAccepted}
                onChange={(e) => handleInputChange('termsAccepted', e.target.checked)}
              />
              <label htmlFor="terms-checkbox" className="ml-3 text-sm text-slate-600">
                I agree to the{' '}
                <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">
                  Privacy Policy
                </a>
              </label>
            </div>
            {errors.termsAccepted && (
              <div className="error-message">
                <AlertCircle className="w-3 h-3" />
                {errors.termsAccepted}
              </div>
            )}
          </div>
        );

      case 5: // Personal Details
        return (
          <div className="space-y-6">
            <div className="mb-8">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">
                Your Personal Details
              </h2>
              <p className="text-slate-500">We need this to verify your account.</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="form-group">
                <label htmlFor="first-name" className="block text-sm font-semibold text-slate-700 mb-2">
                  First Name
                </label>
                <div className="relative">
                  <User className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <input
                    type="text"
                    id="first-name"
                    name="firstName"
                    required
                    className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.firstName ? 'error' : ''}`}
                    placeholder="Your first name"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                  />
                </div>
                {errors.firstName && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.firstName}
                  </div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="last-name" className="block text-sm font-semibold text-slate-700 mb-2">
                  Last Name
                </label>
                <div className="relative">
                  <User className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <input
                    type="text"
                    id="last-name"
                    name="lastName"
                    required
                    className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.lastName ? 'error' : ''}`}
                    placeholder="Your last name"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                  />
                </div>
                {errors.lastName && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.lastName}
                  </div>
                )}
              </div>

              <div className="form-group md:col-span-2">
                <label htmlFor="phone" className="block text-sm font-semibold text-slate-700 mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <Phone className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    required
                    className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.phone ? 'error' : ''}`}
                    placeholder="e.g. 07123456789"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                  />
                </div>
                {errors.phone && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.phone}
                  </div>
                )}
              </div>

              <div className="form-group md:col-span-2">
                <label htmlFor="address" className="block text-sm font-semibold text-slate-700 mb-2">
                  Address
                </label>
                <div className="relative">
                  <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <input
                    type="text"
                    id="address"
                    name="address"
                    required
                    className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.address ? 'error' : ''}`}
                    placeholder="Street address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                  />
                </div>
                {errors.address && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.address}
                  </div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="city" className="block text-sm font-semibold text-slate-700 mb-2">
                  City
                </label>
                <div className="relative">
                  <Building className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <input
                    type="text"
                    id="city"
                    name="city"
                    required
                    className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.city ? 'error' : ''}`}
                    placeholder="Your city"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                  />
                </div>
                {errors.city && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.city}
                  </div>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="postcode" className="block text-sm font-semibold text-slate-700 mb-2">
                  Postcode
                </label>
                <div className="relative">
                  <Mail className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                  <input
                    type="text"
                    id="postcode"
                    name="postcode"
                    required
                    className={`form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.postcode ? 'error' : ''}`}
                    placeholder="e.g. SW1A 0AA"
                    value={formData.postcode}
                    onChange={(e) => handleInputChange('postcode', e.target.value)}
                  />
                </div>
                {errors.postcode && (
                  <div className="error-message">
                    <AlertCircle className="w-3 h-3" />
                    {errors.postcode}
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Progress indicator component
  const renderProgressIndicator = () => (
    <div className="w-full flex items-center">
      {stepConfig.map((step, index) => {
        const isCompleted = index < currentStep;
        const isActive = index === currentStep;

        return (
          <div key={step.id} className="flex-1 flex flex-col items-center relative">
            <div className={`w-10 h-10 rounded-full border-2 flex items-center justify-center text-sm font-semibold mb-2 transition-all duration-300 ${
              isCompleted
                ? 'bg-green-500 border-green-500 text-white'
                : isActive
                  ? 'bg-blue-500 border-blue-500 text-white'
                  : 'border-slate-300 bg-slate-100 text-slate-500'
            }`}>
              {isCompleted ? <Check className="w-4 h-4" /> : index + 1}
            </div>
            <span className={`text-xs text-center font-medium ${
              isActive ? 'text-slate-800' : 'text-slate-500'
            }`}>
              {step.label}
            </span>
            {index < stepConfig.length - 1 && (
              <div className={`absolute top-5 left-full w-full h-0.5 transition-all duration-300 ${
                isCompleted ? 'bg-green-500' : 'bg-slate-200'
              }`} />
            )}
          </div>
        );
      })}
    </div>
  );
  // Main component return
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-5xl flex flex-col glass-card rounded-2xl overflow-hidden my-8">
        {/* Top Panel: Progress & Branding */}
        <div className="w-full p-8 bg-gradient-to-br from-slate-50 to-slate-100 border-b border-slate-200">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                <ChefHat className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                RestroManager
              </h1>
            </div>
            <div className="text-sm text-slate-500">
              Step {currentStep + 1} of {stepConfig.length} - {stepConfig[currentStep].label}
            </div>
          </div>

          {renderProgressIndicator()}
        </div>

        {/* Bottom Panel: Form */}
        <div className="w-full p-8 sm:p-14">
          <form onSubmit={(e) => e.preventDefault()}>
            {renderStepContent()}

            {/* Navigation Buttons */}
            <div className="mt-8 flex items-center gap-4">
              {currentStep > 0 && (
                <button
                  type="button"
                  onClick={handlePrevious}
                  className="btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all flex items-center gap-2 border border-slate-300 hover:border-slate-400"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back
                </button>
              )}

              {currentStep < stepConfig.length - 1 ? (
                <button
                  type="button"
                  onClick={handleNext}
                  disabled={isLoading}
                  className="btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <div className="loading-spinner"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      Next Step
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isLoading}
                  className="btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <div className="loading-spinner"></div>
                      Creating Account...
                    </>
                  ) : (
                    'Create Account'
                  )}
                </button>
              )}
            </div>
          </form>
        </div>
      </div>

      {/* Add Section Modal */}
      {showAddSectionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-2xl shadow-2xl max-w-sm w-full mx-4">
            <h3 className="text-xl font-bold text-slate-800 mb-4">Add New Section</h3>
            <div className="form-group">
              <label htmlFor="modal-section-name" className="block text-sm font-semibold text-slate-700 mb-2">
                Section Name
              </label>
              <input
                type="text"
                id="modal-section-name"
                placeholder="e.g. Patio, Bar Area"
                className="form-input w-full px-4 py-3 border border-slate-300 rounded-xl"
                value={newSectionName}
                onChange={(e) => setNewSectionName(e.target.value)}
              />
            </div>
            <div className="mt-6 flex justify-end gap-4">
              <button
                type="button"
                onClick={() => {
                  setShowAddSectionModal(false);
                  setNewSectionName('');
                }}
                className="btn-secondary px-4 py-2 text-sm font-semibold rounded-lg border border-slate-300 hover:border-slate-400 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => {
                  if (newSectionName.trim()) {
                    addTableSection(newSectionName.trim());
                    setShowAddSectionModal(false);
                    setNewSectionName('');
                  }
                }}
                className="btn-primary px-4 py-2 text-sm font-semibold rounded-lg"
              >
                Add Section
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Success/Error Modal */}
      {showModal && (
        <Modal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          title={modalContent.title}
        >
          <p className="text-slate-600 mb-6">{modalContent.message}</p>
          <div className="flex justify-end">
            <Button onClick={() => setShowModal(false)}>
              Close
            </Button>
          </div>
        </Modal>
      )}

      {/* Login Link */}
      <div className="fixed bottom-4 right-4">
        <p className="text-sm text-slate-600">
          Already have an account?{' '}
          <Link to="/login" className="text-blue-600 hover:text-blue-800 font-medium">
            Sign in
          </Link>
        </p>
      </div>
    </div>
  );
};
